#!/bin/bash
# FleetXQ BulkImporter Environment Setup Script
# Usage: ./environment-setup.sh <environment> [options]

set -e

ENVIRONMENT=""
SETUP_DATABASE=false
CONFIGURE_SECRETS=false
INSTALL_SERVICE=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        development|staging|pilot|production)
            ENVIRONMENT="$1"
            shift
            ;;
        --setup-database)
            SETUP_DATABASE=true
            shift
            ;;
        --configure-secrets)
            CONFIGURE_SECRETS=true
            shift
            ;;
        --install-service)
            INSTALL_SERVICE=true
            shift
            ;;
        -h|--help)
            echo "Usage: $0 <environment> [options]"
            echo "Environments: development, staging, pilot, production"
            echo "Options:"
            echo "  --setup-database    Create database objects"
            echo "  --configure-secrets Configure environment secrets"
            echo "  --install-service   Install as system service"
            echo "  -h, --help          Show this help"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

if [[ -z "$ENVIRONMENT" ]]; then
    echo "Error: Environment must be specified"
    echo "Usage: $0 <environment> [options]"
    exit 1
fi

echo "==== FleetXQ BulkImporter Environment Setup ===="
echo "Environment: $ENVIRONMENT"
echo "Setup Database: $SETUP_DATABASE"
echo "Configure Secrets: $CONFIGURE_SECRETS"
echo "Install Service: $INSTALL_SERVICE"
echo ""

# Environment-specific configurations
case $ENVIRONMENT in
    development)
        APP_DIR="/opt/fleetxq/bulkimporter-dev"
        SERVICE_NAME="fleetxq-bulkimporter-dev"
        ;;
    staging)
        APP_DIR="/opt/fleetxq/bulkimporter-staging"
        SERVICE_NAME="fleetxq-bulkimporter-staging"
        ;;
    pilot)
        APP_DIR="/opt/fleetxq/bulkimporter-pilot"
        SERVICE_NAME="fleetxq-bulkimporter-pilot"
        ;;
    production)
        APP_DIR="/opt/fleetxq/bulkimporter"
        SERVICE_NAME="fleetxq-bulkimporter"
        ;;
    *)
        echo "Invalid environment: $ENVIRONMENT"
        exit 1
        ;;
esac

# Create application directory structure
echo "Creating application directories..."
sudo mkdir -p "$APP_DIR"
sudo mkdir -p "$APP_DIR/logs"
sudo mkdir -p "$APP_DIR/data/output"
sudo mkdir -p "$APP_DIR/data/archive"
sudo mkdir -p "$APP_DIR/data/errors"
sudo mkdir -p "/etc/fleetxq/bulkimporter/$ENVIRONMENT"

# Set up user and permissions
echo "Configuring user and permissions..."
if ! id "fleetxq" &>/dev/null; then
    sudo useradd -r -s /bin/false -d "$APP_DIR" fleetxq
fi

sudo chown -R fleetxq:fleetxq "$APP_DIR"
sudo chmod 755 "$APP_DIR"
sudo chmod 750 "$APP_DIR/logs"
sudo chmod 750 "$APP_DIR/data"

# Set environment variable
echo "export ASPNETCORE_ENVIRONMENT=$ENVIRONMENT" | sudo tee /etc/environment.d/fleetxq-bulkimporter.conf

# Setup database (if requested)
if [[ "$SETUP_DATABASE" == true ]]; then
    echo "Setting up database objects..."
    
    if [[ ! -f "../Sql/001-CreateStagingSchema.sql" ]]; then
        echo "Error: SQL scripts not found. Ensure you're running from the correct directory."
        exit 1
    fi
    
    echo "Database setup requires manual execution of SQL scripts:"
    echo "  1. 001-CreateStagingSchema.sql"
    echo "  2. 002-CreateDriverStagingTable.sql"
    echo "  3. 003-CreateVehicleStagingTable.sql"
    echo "  4. 004-CreateValidationProcedures.sql"
    echo "  5. 005-CreateMergeProcedures.sql"
    echo "  6. 006-CreateDataGenerationProcedures.sql"
    echo "  7. 007-EnhanceModuleTracking.sql"
    echo "  8. 008-UpdateStagingTablesForDealerScoping.sql"
    echo "  9. 009-CreateModuleAvailabilityView.sql"
    echo "  10. 010-CreateDealerValidationProcedures.sql"
    echo ""
    echo "Execute these scripts against your $ENVIRONMENT database."
fi

# Configure secrets (if requested)
if [[ "$CONFIGURE_SECRETS" == true ]]; then
    echo "Configuring environment secrets..."
    
    SECRETS_DIR="/etc/fleetxq/bulkimporter/$ENVIRONMENT"
    sudo mkdir -p "$SECRETS_DIR"
    
    case $ENVIRONMENT in
        development)
            echo "Development environment uses local configuration files."
            ;;
        staging|pilot|production)
            echo "Creating secrets configuration template..."
            
            cat <<EOF | sudo tee "$SECRETS_DIR/secrets.json"
{
  "ConnectionStrings": {
    "FleetXQConnection": "REPLACE_WITH_ACTUAL_CONNECTION_STRING"
  },
  "Environment": {
    "NotificationWebhooks": [
      "REPLACE_WITH_WEBHOOK_URL"
    ]
  }
}
EOF
            
            sudo chown fleetxq:fleetxq "$SECRETS_DIR/secrets.json"
            sudo chmod 600 "$SECRETS_DIR/secrets.json"
            
            echo "Secrets template created at: $SECRETS_DIR/secrets.json"
            echo "IMPORTANT: Replace placeholder values with actual secrets!"
            ;;
    esac
fi

# Install as system service (if requested)
if [[ "$INSTALL_SERVICE" == true ]]; then
    echo "Installing system service..."
    
    cat <<EOF | sudo tee "/etc/systemd/system/$SERVICE_NAME.service"
[Unit]
Description=FleetXQ BulkImporter ($ENVIRONMENT)
After=network.target
Wants=network.target

[Service]
Type=notify
User=fleetxq
Group=fleetxq
WorkingDirectory=$APP_DIR
ExecStart=$APP_DIR/FleetXQ.Tools.BulkImporter
Environment=ASPNETCORE_ENVIRONMENT=$ENVIRONMENT
Environment=DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=false
Restart=on-failure
RestartSec=10
KillSignal=SIGINT
SyslogIdentifier=fleetxq-bulkimporter-$ENVIRONMENT
PrivateTmp=true
ProtectSystem=strict
ReadWritePaths=$APP_DIR
NoNewPrivileges=true
ProtectHome=true

[Install]
WantedBy=multi-user.target
EOF

    sudo systemctl daemon-reload
    sudo systemctl enable "$SERVICE_NAME"
    
    echo "Service installed: $SERVICE_NAME"
    echo "Start with: sudo systemctl start $SERVICE_NAME"
    echo "Check status: sudo systemctl status $SERVICE_NAME"
    echo "View logs: sudo journalctl -u $SERVICE_NAME -f"
fi

echo ""
echo "Environment setup completed successfully!"
echo "Application directory: $APP_DIR"

if [[ "$ENVIRONMENT" == "production" ]]; then
    echo ""
    echo "PRODUCTION ENVIRONMENT WARNINGS:"
    echo "- Ensure database connection string is secure"
    echo "- Configure monitoring and alerting"
    echo "- Test backup and recovery procedures"
    echo "- Review security hardening checklist"
fi
