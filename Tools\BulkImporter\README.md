# FleetXQ Bulk Importer

A .NET console application for performing efficient bulk insertion of driver and vehicle data into the FleetXQ database.

## Prerequisites

- .NET 6.0 or later
- SQL Server with FleetXQ database
- Database permissions for creating schemas, tables, and stored procedures

## First-Time Setup

1. **Build the project**:
   ```bash
   dotnet restore
   dotnet build
   ```

2. **Setup database**:
   - Ensure SQL Server is accessible
   - Run SQL scripts in order (001 through 006) from the `Sql/` directory
   - Update connection string in `appsettings.json`

3. **Configure connection**:
   - Edit `appsettings.json`
   - Set `ConnectionStrings:FleetXQConnection` to your database

### Running

#### Interactive Mode (Default)
```bash
BulkImporter
```

This will prompt you for:
- Number of drivers to process (default: 10,000)
- Number of vehicles to process (default: 5,000)
- Batch size for operations (default: 1,000)
- Data source (generate synthetic data or read from files)
- Dry run confirmation

#### Non-Interactive Mode
```bash
BulkImporter --drivers 1000 --vehicles 500 --non-interactive
```

#### Command Line Options
```bash
# Basic usage
BulkImporter --drivers 5000 --vehicles 2500

# Dry run with synthetic data
BulkImporter --generate --dry-run

# Custom batch size
BulkImporter --batch-size 5000 --non-interactive

# Generate synthetic data
BulkImporter --generate --drivers 1000 --vehicles 500

# Show help
BulkImporter --help
```

## Basic Usage

```bash
# Interactive mode (will prompt for settings)
dotnet run

# Non-interactive with specific counts
dotnet run -- --drivers 1000 --vehicles 500 --non-interactive

# Dry run (no database changes)
dotnet run -- --generate --dry-run

# Help
dotnet run -- --help
```

## Configuration

The main configuration is in `appsettings.json`. Key settings:

- **ConnectionStrings:FleetXQConnection**: Your SQL Server connection string
- **BulkImporter:DefaultDriversCount**: Default number of drivers (10000)
- **BulkImporter:DefaultVehiclesCount**: Default number of vehicles (5000)
- **BulkImporter:DealerValidationEnabled**: Enable dealer-specific validation (true)
- **BulkImporter:RequireDealerSelection**: Require dealer selection before import (true)
- **BulkImporter:DefaultDealerId**: Optional default dealer GUID
- **BulkImporter:DefaultDealerName**: Optional default dealer name

## Dealer Scoping (NEW FEATURE)

The bulk importer now supports dealer-specific scoping and validation:

### Features
- **Dealer Selection**: Choose dealer before import to scope operations
- **Customer Validation**: Ensures customers belong to selected dealer
- **Module Availability**: Validates spare module availability per dealer
- **Assignment Tracking**: Tracks module assignments and history

### Dealer Workflow
1. **Select Dealer**: Choose dealer by ID or name
2. **Validate Scope**: System validates customer and module access
3. **Import Data**: Process drivers/vehicles within dealer boundaries
4. **Track Changes**: Automatic history tracking for module assignments

### Command Line Options (Enhanced)
```bash
# With dealer ID
BulkImporter --dealer-id [GUID] --drivers 1000 --vehicles 500

# With dealer name
BulkImporter --dealer-name "Dealer Name" --drivers 1000 --vehicles 500

# Disable dealer validation
BulkImporter --no-dealer-validation --drivers 1000 --vehicles 500

# Environment-specific execution
BulkImporter --environment Production --drivers 50000 --vehicles 25000
```

## Environment Support (NEW FEATURE)

The bulk importer now supports multiple deployment environments:

### Supported Environments
- **Development**: Local development with minimal security
- **Staging**: Integration testing environment  
- **Pilot**: Limited production testing
- **Production**: Full production environment

### Environment Configuration
Each environment has its own configuration file (`appsettings.{Environment}.json`) with environment-specific settings for:
- Database connections
- Batch sizes and limits
- Logging levels
- File paths
- Security settings
- Notification webhooks

### Usage
```bash
# Set environment via environment variable
export ASPNETCORE_ENVIRONMENT=Production
dotnet run

# Or specify via command line
dotnet run --environment Staging

# Use environment-specific settings
dotnet run --environment Production --drivers 50000 --non-interactive
```

For detailed deployment instructions, see [DEPLOYMENT.md](DEPLOYMENT.md).

- **BulkImporter:DefaultBatchSize**: Processing batch size (1000)