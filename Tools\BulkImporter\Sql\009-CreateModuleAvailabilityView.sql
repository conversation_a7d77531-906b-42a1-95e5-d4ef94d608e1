-- =============================================
-- FleetXQ Bulk Importer - Module Availability View
-- Creates views for tracking module availability and assignments
-- =============================================

-- Create comprehensive module availability view
IF OBJECT_ID('[dbo].[vw_AvailableModules]', 'V') IS NOT NULL
    DROP VIEW [dbo].[vw_AvailableModules]
GO

CREATE VIEW [dbo].[vw_AvailableModules] AS
SELECT 
    m.[Id] as [ModuleId],
    m.[TechNumber],
    m.[DealerId],
    m.[ModuleStatus],
    m.[IsAllocatedToVehicle],
    m.[IoTDevice],
    m.[Status] as [ModuleInternalStatus],
    m.[ModuleType],
    m.[IsActive],
    v.[HireNo] as [AssignedVehicleHireNo],
    v.[Id] as [AssignedVehicleId],
    v.[SerialNo] as [AssignedVehicleSerialNo],
    v.[Description] as [AssignedVehicleDescription],
    v.[OnHire] as [AssignedVehicleOnHire],
    d.[Name] as [DealerName],
    d.[Active] as [DealerActive],
    c.[CompanyName] as [AssignedCustomerName],
    c.[Id] as [AssignedCustomerId],
    -- Availability calculation
    CASE 
        WHEN m.[IsActive] = 0 THEN 'Inactive'
        WHEN m.[ModuleStatus] = 'Available' AND (m.[IsAllocatedToVehicle] = 0 OR m.[IsAllocatedToVehicle] IS NULL) THEN 'Available'
        WHEN m.[ModuleStatus] = 'Assigned' AND m.[IsAllocatedToVehicle] = 1 THEN 'Assigned'
        WHEN m.[ModuleStatus] = 'Maintenance' THEN 'Maintenance'
        WHEN m.[ModuleStatus] = 'Retired' THEN 'Retired'
        ELSE 'Unknown'
    END as [AvailabilityStatus],
    -- Last allocation information
    (SELECT TOP 1 mah.[AllocationDate] 
     FROM [dbo].[ModuleAllocationHistory] mah 
     WHERE mah.[ModuleId] = m.[Id] AND mah.[DeallocationDate] IS NULL
     ORDER BY mah.[AllocationDate] DESC) as [LastAllocationDate],
    (SELECT TOP 1 mah.[AllocationType] 
     FROM [dbo].[ModuleAllocationHistory] mah 
     WHERE mah.[ModuleId] = m.[Id] AND mah.[DeallocationDate] IS NULL
     ORDER BY mah.[AllocationDate] DESC) as [LastAllocationType]
FROM [dbo].[Module] m
LEFT JOIN [dbo].[Vehicle] v ON m.[Id] = v.[ModuleId1]
INNER JOIN [dbo].[Dealer] d ON m.[DealerId] = d.[Id]
LEFT JOIN [dbo].[Customer] c ON v.[CustomerId] = c.[Id];
GO

-- Create dealer-specific available modules view
IF OBJECT_ID('[dbo].[vw_DealerAvailableModules]', 'V') IS NOT NULL
    DROP VIEW [dbo].[vw_DealerAvailableModules]
GO

CREATE VIEW [dbo].[vw_DealerAvailableModules] AS
SELECT 
    [DealerId],
    [DealerName],
    [ModuleId],
    [TechNumber],
    [IoTDevice],
    [ModuleType],
    [AvailabilityStatus],
    [LastAllocationDate],
    [LastAllocationType]
FROM [dbo].[vw_AvailableModules]
WHERE [AvailabilityStatus] = 'Available' 
    AND [DealerActive] = 1
    AND [IsActive] = 1;
GO

-- Create module assignment summary view
IF OBJECT_ID('[dbo].[vw_ModuleAssignmentSummary]', 'V') IS NOT NULL
    DROP VIEW [dbo].[vw_ModuleAssignmentSummary]
GO

CREATE VIEW [dbo].[vw_ModuleAssignmentSummary] AS
SELECT 
    d.[Id] as [DealerId],
    d.[Name] as [DealerName],
    d.[Active] as [DealerActive],
    COUNT(m.[Id]) as [TotalModules],
    SUM(CASE WHEN m.[AvailabilityStatus] = 'Available' THEN 1 ELSE 0 END) as [AvailableModules],
    SUM(CASE WHEN m.[AvailabilityStatus] = 'Assigned' THEN 1 ELSE 0 END) as [AssignedModules],
    SUM(CASE WHEN m.[AvailabilityStatus] = 'Maintenance' THEN 1 ELSE 0 END) as [MaintenanceModules],
    SUM(CASE WHEN m.[AvailabilityStatus] = 'Retired' THEN 1 ELSE 0 END) as [RetiredModules],
    SUM(CASE WHEN m.[AvailabilityStatus] = 'Inactive' THEN 1 ELSE 0 END) as [InactiveModules],
    -- Percentage calculations
    CASE 
        WHEN COUNT(m.[Id]) > 0 THEN 
            ROUND(CAST(SUM(CASE WHEN m.[AvailabilityStatus] = 'Available' THEN 1 ELSE 0 END) AS FLOAT) / COUNT(m.[Id]) * 100, 2)
        ELSE 0 
    END as [AvailabilityPercentage]
FROM [dbo].[Dealer] d
LEFT JOIN [dbo].[vw_AvailableModules] m ON d.[Id] = m.[DealerId]
GROUP BY d.[Id], d.[Name], d.[Active];
GO

-- Create module allocation history view with enhanced details
IF OBJECT_ID('[dbo].[vw_ModuleAllocationHistory]', 'V') IS NOT NULL
    DROP VIEW [dbo].[vw_ModuleAllocationHistory]
GO

CREATE VIEW [dbo].[vw_ModuleAllocationHistory] AS
SELECT 
    mah.[Id] as [AllocationHistoryId],
    mah.[AllocationDate],
    mah.[DeallocationDate],
    mah.[AllocationType],
    mah.[RequestedBy],
    mah.[Notes],
    mah.[ImportSessionId],
    m.[Id] as [ModuleId],
    m.[TechNumber] as [ModuleTechNumber],
    m.[IoTDevice] as [ModuleIoTDevice],
    v.[Id] as [VehicleId],
    v.[HireNo] as [VehicleHireNo],
    v.[SerialNo] as [VehicleSerialNo],
    v.[Description] as [VehicleDescription],
    d.[Id] as [DealerId],
    d.[Name] as [DealerName],
    c.[Id] as [CustomerId],
    c.[CompanyName] as [CustomerName],
    s.[Id] as [SiteId],
    s.[SiteName],
    -- Duration calculation
    CASE 
        WHEN mah.[DeallocationDate] IS NOT NULL THEN 
            DATEDIFF(DAY, mah.[AllocationDate], mah.[DeallocationDate])
        ELSE 
            DATEDIFF(DAY, mah.[AllocationDate], GETUTCDATE())
    END as [AllocationDurationDays],
    -- Status
    CASE 
        WHEN mah.[DeallocationDate] IS NULL THEN 'Active'
        ELSE 'Completed'
    END as [AllocationStatus]
FROM [dbo].[ModuleAllocationHistory] mah
INNER JOIN [dbo].[Module] m ON mah.[ModuleId] = m.[Id]
LEFT JOIN [dbo].[Vehicle] v ON mah.[VehicleId] = v.[Id]
INNER JOIN [dbo].[Dealer] d ON mah.[DealerId] = d.[Id]
LEFT JOIN [dbo].[Customer] c ON v.[CustomerId] = c.[Id]
LEFT JOIN [dbo].[Site] s ON v.[SiteId] = s.[Id];
GO

PRINT 'Created module availability and tracking views successfully';
