-- =============================================
-- FleetXQ Bulk Importer - Enhanced Staging Tables for Dealer Scoping
-- Adds dealer validation and scoping fields to staging tables
-- =============================================

-- Enhance ImportSession table with dealer selection
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('[Staging].[ImportSession]') AND name = 'SelectedDealerId')
BEGIN
    ALTER TABLE [Staging].[ImportSession]
    ADD [SelectedDealerId] UNIQUEIDENTIFIER NULL,
        [SelectedDealerName] NVARCHAR(100) NULL,
        [DealerValidationEnabled] BIT NOT NULL DEFAULT 1;
    
    PRINT 'Added dealer selection fields to ImportSession table';
END
ELSE
BEGIN
    PRINT 'Dealer selection fields already exist in ImportSession table';
END
GO

-- Add dealer validation to DriverImport staging table
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('[Staging].[DriverImport]') AND name = 'DealerId')
BEGIN
    ALTER TABLE [Staging].[DriverImport]
    ADD [DealerId] UNIQUEIDENTIFIER NULL,
        [DealerValidationError] NVARCHAR(MAX) NULL,
        [CustomerDealerId] UNIQUEIDENTIFIER NULL; -- Resolved dealer ID from customer lookup
    
    -- Add index for dealer-based queries
    CREATE INDEX IX_DriverImport_Dealer ON [Staging].[DriverImport]([ImportSessionId], [DealerId]) 
    WHERE [DealerId] IS NOT NULL;
    
    PRINT 'Added dealer validation fields to DriverImport table';
END
ELSE
BEGIN
    PRINT 'Dealer validation fields already exist in DriverImport table';
END
GO

-- Add dealer validation to VehicleImport staging table
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('[Staging].[VehicleImport]') AND name = 'DealerId')
BEGIN
    ALTER TABLE [Staging].[VehicleImport] 
    ADD [DealerId] UNIQUEIDENTIFIER NULL,
        [DealerValidationError] NVARCHAR(MAX) NULL,
        [CustomerDealerId] UNIQUEIDENTIFIER NULL, -- Resolved dealer ID from customer lookup
        [ModuleDealerId] UNIQUEIDENTIFIER NULL,   -- Resolved dealer ID from module lookup
        [ModuleAvailabilityStatus] NVARCHAR(50) NULL; -- Available, Assigned, NotFound, WrongDealer
    
    -- Add index for dealer-based queries
    CREATE INDEX IX_VehicleImport_Dealer ON [Staging].[VehicleImport]([ImportSessionId], [DealerId]) 
    WHERE [DealerId] IS NOT NULL;
    
    PRINT 'Added dealer validation fields to VehicleImport table';
END
ELSE
BEGIN
    PRINT 'Dealer validation fields already exist in VehicleImport table';
END
GO

-- Create staging table for dealer-customer validation cache
IF NOT EXISTS (SELECT * FROM sys.tables WHERE schema_id = SCHEMA_ID('Staging') AND name = 'DealerCustomerCache')
BEGIN
    CREATE TABLE [Staging].[DealerCustomerCache] (
        [Id] BIGINT IDENTITY(1,1) PRIMARY KEY,
        [ImportSessionId] UNIQUEIDENTIFIER NOT NULL,
        [DealerId] UNIQUEIDENTIFIER NOT NULL,
        [CustomerId] UNIQUEIDENTIFIER NOT NULL,
        [CustomerName] NVARCHAR(100) NOT NULL,
        [IsValid] BIT NOT NULL DEFAULT 1,
        [CacheCreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        
        -- Foreign key to import session
        CONSTRAINT FK_DealerCustomerCache_ImportSession 
            FOREIGN KEY ([ImportSessionId]) REFERENCES [Staging].[ImportSession]([Id]),
            
        -- Unique constraint to prevent duplicates
        CONSTRAINT UX_DealerCustomerCache_Session_Customer 
            UNIQUE ([ImportSessionId], [CustomerId]),
            
        -- Indexes for performance
        INDEX IX_DealerCustomerCache_Session_Dealer ([ImportSessionId], [DealerId]),
        INDEX IX_DealerCustomerCache_Customer ([CustomerName], [DealerId])
    );
    
    PRINT 'Created DealerCustomerCache staging table';
END
ELSE
BEGIN
    PRINT 'DealerCustomerCache staging table already exists';
END
GO

-- Create staging table for dealer-module validation cache
IF NOT EXISTS (SELECT * FROM sys.tables WHERE schema_id = SCHEMA_ID('Staging') AND name = 'DealerModuleCache')
BEGIN
    CREATE TABLE [Staging].[DealerModuleCache] (
        [Id] BIGINT IDENTITY(1,1) PRIMARY KEY,
        [ImportSessionId] UNIQUEIDENTIFIER NOT NULL,
        [DealerId] UNIQUEIDENTIFIER NOT NULL,
        [ModuleId] UNIQUEIDENTIFIER NOT NULL,
        [ModuleSerialNumber] NVARCHAR(50) NOT NULL,
        [ModuleStatus] NVARCHAR(20) NOT NULL,
        [IsAllocatedToVehicle] BIT NOT NULL,
        [AssignedVehicleId] UNIQUEIDENTIFIER NULL,
        [AssignedVehicleHireNo] NVARCHAR(50) NULL,
        [IsAvailable] BIT NOT NULL DEFAULT 0,
        [CacheCreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        
        -- Foreign key to import session
        CONSTRAINT FK_DealerModuleCache_ImportSession 
            FOREIGN KEY ([ImportSessionId]) REFERENCES [Staging].[ImportSession]([Id]),
            
        -- Unique constraint to prevent duplicates
        CONSTRAINT UX_DealerModuleCache_Session_Module 
            UNIQUE ([ImportSessionId], [ModuleId]),
            
        -- Indexes for performance
        INDEX IX_DealerModuleCache_Session_Dealer ([ImportSessionId], [DealerId]),
        INDEX IX_DealerModuleCache_Serial ([ModuleSerialNumber], [DealerId]),
        INDEX IX_DealerModuleCache_Available ([ImportSessionId], [IsAvailable])
    );
    
    PRINT 'Created DealerModuleCache staging table';
END
ELSE
BEGIN
    PRINT 'DealerModuleCache staging table already exists';
END
GO

-- Add foreign key constraints for dealer validation
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_DriverImport_Dealer')
BEGIN
    ALTER TABLE [Staging].[DriverImport]
    ADD CONSTRAINT FK_DriverImport_Dealer 
        FOREIGN KEY ([DealerId]) REFERENCES [dbo].[Dealer]([Id]);
    
    PRINT 'Added FK constraint: DriverImport -> Dealer';
END
GO

IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_VehicleImport_Dealer')
BEGIN
    ALTER TABLE [Staging].[VehicleImport]
    ADD CONSTRAINT FK_VehicleImport_Dealer 
        FOREIGN KEY ([DealerId]) REFERENCES [dbo].[Dealer]([Id]);
    
    PRINT 'Added FK constraint: VehicleImport -> Dealer';
END
GO

IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_ImportSession_Dealer')
BEGIN
    ALTER TABLE [Staging].[ImportSession]
    ADD CONSTRAINT FK_ImportSession_Dealer 
        FOREIGN KEY ([SelectedDealerId]) REFERENCES [dbo].[Dealer]([Id]);
    
    PRINT 'Added FK constraint: ImportSession -> Dealer';
END
GO

PRINT 'Enhanced staging tables for dealer scoping completed successfully';
