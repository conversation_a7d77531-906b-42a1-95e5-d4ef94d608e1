{"ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=FleetXQ;Trusted_Connection=true;MultipleActiveResultSets=true;", "FleetXQConnection": "Server=(localdb)\\mssqllocaldb;Database=FleetXQ;Trusted_Connection=true;MultipleActiveResultSets=true;"}, "BulkImporter": {"DefaultDriversCount": 10000, "DefaultVehiclesCount": 5000, "DefaultBatchSize": 1000, "MaxBatchSize": 50000, "BulkCopyTimeout": 300, "CommandTimeout": 120, "NotifyAfter": 1000, "EnableRetry": true, "MaxRetryAttempts": 3, "RetryDelaySeconds": 5, "ValidationEnabled": true, "StopOnFirstError": false, "DealerValidationEnabled": true, "RequireDealerSelection": true, "DefaultDealerId": null, "DefaultDealerName": null}, "DataGeneration": {"OutputDirectory": "OutputFiles", "ArchiveDirectory": "Archive", "ErrorDirectory": "Errors", "EnableSyntheticDataGeneration": true, "RandomSeed": 42, "GenerationBatchSize": 5000, "ValidateGeneratedData": true, "MaxMemoryUsageMB": 1000}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": "Information", "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/bulkimporter-.log", "rollingInterval": "Day", "retainedFileCountLimit": 30, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {CorrelationId} {Message:lj} {Properties:j}{NewLine}{Exception}"}}], "Enrich": ["FromLogContext"], "Properties": {"Application": "FleetXQ.BulkImporter"}}}