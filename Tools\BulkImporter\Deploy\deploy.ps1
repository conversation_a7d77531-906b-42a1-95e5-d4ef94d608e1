# FleetXQ BulkImporter Deployment Script
# Usage: .\deploy.ps1 -Environment <env> -Version <version> [-ConfigureSecrets]

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("Development", "Staging", "Pilot", "Production")]
    [string]$Environment,
    
    [Parameter(Mandatory=$true)]
    [string]$Version,
    
    [switch]$ConfigureSecrets,
    
    [string]$TargetPath = "C:\Apps\FleetXQ\BulkImporter",
    
    [string]$ServiceName = "FleetXQ.BulkImporter"
)

$ErrorActionPreference = "Stop"

Write-Host "==== FleetXQ BulkImporter Deployment ====" -ForegroundColor Green
Write-Host "Environment: $Environment" -ForegroundColor Yellow
Write-Host "Version: $Version" -ForegroundColor Yellow
Write-Host "Target Path: $TargetPath" -ForegroundColor Yellow

# Environment-specific configurations
$envConfig = @{
    "Development" = @{
        "PublishProfile" = "FolderProfile"
        "RuntimeIdentifier" = "win-x64"
        "SelfContained" = $false
    }
    "Staging" = @{
        "PublishProfile" = "FolderProfile" 
        "RuntimeIdentifier" = "linux-x64"
        "SelfContained" = $true
    }
    "Pilot" = @{
        "PublishProfile" = "FolderProfile"
        "RuntimeIdentifier" = "linux-x64" 
        "SelfContained" = $true
    }
    "Production" = @{
        "PublishProfile" = "FolderProfile"
        "RuntimeIdentifier" = "linux-x64"
        "SelfContained" = $true
    }
}

$config = $envConfig[$Environment]

try {
    # Step 1: Build and publish application
    Write-Host "Step 1: Building application..." -ForegroundColor Cyan
    
    $publishArgs = @(
        "publish"
        "--configuration", "Release"
        "--runtime", $config.RuntimeIdentifier
        "--output", "bin/publish/$Environment"
        "--verbosity", "minimal"
    )
    
    if ($config.SelfContained) {
        $publishArgs += "--self-contained", "true"
        $publishArgs += "--property:PublishTrimmed=true"
    }
    
    & dotnet @publishArgs
    if ($LASTEXITCODE -ne 0) { throw "Build failed" }
    
    # Step 2: Prepare deployment package
    Write-Host "Step 2: Preparing deployment package..." -ForegroundColor Cyan
    
    $publishDir = "bin/publish/$Environment"
    $packageDir = "bin/package/$Environment"
    
    if (Test-Path $packageDir) {
        Remove-Item $packageDir -Recurse -Force
    }
    New-Item -Path $packageDir -ItemType Directory -Force | Out-Null
    
    # Copy application files
    Copy-Item "$publishDir/*" $packageDir -Recurse -Force
    
    # Copy environment-specific configuration
    $configSource = "appsettings.$Environment.json"
    if (Test-Path $configSource) {
        Copy-Item $configSource "$packageDir/appsettings.$Environment.json" -Force
        Write-Host "Copied environment configuration: $configSource" -ForegroundColor Green
    }
    
    # Step 3: Create deployment archive
    Write-Host "Step 3: Creating deployment archive..." -ForegroundColor Cyan
    
    $archiveName = "FleetXQ.BulkImporter.$Environment.$Version.zip"
    $archivePath = "bin/package/$archiveName"
    
    if (Test-Path $archivePath) {
        Remove-Item $archivePath -Force
    }
    
    Compress-Archive -Path "$packageDir/*" -DestinationPath $archivePath -Force
    Write-Host "Created deployment package: $archivePath" -ForegroundColor Green
    
    # Step 4: Configure secrets (if requested)
    if ($ConfigureSecrets) {
        Write-Host "Step 4: Configuring environment secrets..." -ForegroundColor Cyan
        
        switch ($Environment) {
            "Development" {
                Write-Host "Development environment - using local configuration" -ForegroundColor Yellow
            }
            "Staging" {
                Write-Host "Configure staging secrets manually or via Azure Key Vault" -ForegroundColor Yellow
                Write-Host "Required: Database connection string, notification webhooks" -ForegroundColor Yellow
            }
            "Pilot" {
                Write-Host "Configure pilot secrets manually or via Azure Key Vault" -ForegroundColor Yellow
                Write-Host "Required: Database connection string, notification webhooks" -ForegroundColor Yellow
            }
            "Production" {
                Write-Host "PRODUCTION DEPLOYMENT - Ensure all secrets are configured!" -ForegroundColor Red
                Write-Host "Required: Database connection string, notification webhooks" -ForegroundColor Yellow
                Write-Host "Use Azure Key Vault or secure configuration provider" -ForegroundColor Yellow
            }
        }
    }
    
    # Step 5: Environment-specific deployment instructions
    Write-Host "Step 5: Deployment instructions for $Environment..." -ForegroundColor Cyan
    
    switch ($Environment) {
        "Development" {
            Write-Host "Local deployment complete. Run with:" -ForegroundColor Green
            Write-Host "  dotnet FleetXQ.Tools.BulkImporter.dll --environment=Development" -ForegroundColor White
        }
        "Staging" {
            Write-Host "Deploy to staging server:" -ForegroundColor Green
            Write-Host "  1. Copy $archivePath to staging server" -ForegroundColor White
            Write-Host "  2. Extract to application directory" -ForegroundColor White
            Write-Host "  3. Set ASPNETCORE_ENVIRONMENT=Staging" -ForegroundColor White
            Write-Host "  4. Configure database connection string" -ForegroundColor White
            Write-Host "  5. Run: ./FleetXQ.Tools.BulkImporter" -ForegroundColor White
        }
        "Pilot" {
            Write-Host "Deploy to pilot environment:" -ForegroundColor Green
            Write-Host "  1. Copy $archivePath to pilot server" -ForegroundColor White
            Write-Host "  2. Extract to application directory" -ForegroundColor White
            Write-Host "  3. Set ASPNETCORE_ENVIRONMENT=Pilot" -ForegroundColor White
            Write-Host "  4. Configure database connection string" -ForegroundColor White
            Write-Host "  5. Test with dry-run first" -ForegroundColor White
        }
        "Production" {
            Write-Host "PRODUCTION DEPLOYMENT CHECKLIST:" -ForegroundColor Red
            Write-Host "  ✓ Version tested in Staging and Pilot" -ForegroundColor Yellow
            Write-Host "  ✓ Database backup completed" -ForegroundColor Yellow
            Write-Host "  ✓ Maintenance window scheduled" -ForegroundColor Yellow
            Write-Host "  ✓ Rollback plan prepared" -ForegroundColor Yellow
            Write-Host "  ✓ Monitoring alerts configured" -ForegroundColor Yellow
            Write-Host "  " -ForegroundColor White
            Write-Host "Deployment steps:" -ForegroundColor Green
            Write-Host "  1. Deploy during maintenance window" -ForegroundColor White
            Write-Host "  2. Set ASPNETCORE_ENVIRONMENT=Production" -ForegroundColor White
            Write-Host "  3. Configure production database connection" -ForegroundColor White
            Write-Host "  4. Verify configuration before first run" -ForegroundColor White
        }
    }
    
    Write-Host "" -ForegroundColor White
    Write-Host "Deployment preparation completed successfully!" -ForegroundColor Green
    Write-Host "Package location: $archivePath" -ForegroundColor Green
    
} catch {
    Write-Host "Deployment failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
