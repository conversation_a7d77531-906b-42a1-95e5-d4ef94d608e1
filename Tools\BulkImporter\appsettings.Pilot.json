{"ConnectionStrings": {"DefaultConnection": "Server=pilot-db-server;Database=FleetXQ_Pilot;User Id=bulkimporter_app;Password=***********;MultipleActiveResultSets=true;Encrypt=true;TrustServerCertificate=false;", "FleetXQConnection": "Server=pilot-db-server;Database=FleetXQ_Pilot;User Id=bulkimporter_app;Password=***********;MultipleActiveResultSets=true;Encrypt=true;TrustServerCertificate=false;"}, "BulkImporter": {"DefaultDriversCount": 5000, "DefaultVehiclesCount": 2500, "DefaultBatchSize": 500, "MaxBatchSize": 10000, "BulkCopyTimeout": 600, "CommandTimeout": 300, "ValidationEnabled": true, "StopOnFirstError": false, "DealerValidationEnabled": true, "RequireDealerSelection": true, "CleanupStagingData": true}, "DataGeneration": {"OutputDirectory": "/app/data/output", "ArchiveDirectory": "/app/data/archive", "ErrorDirectory": "/app/data/errors", "EnableSyntheticDataGeneration": true, "RandomSeed": 12345, "GenerationBatchSize": 2500, "ValidateGeneratedData": true, "MaxMemoryUsageMB": 2000}, "Serilog": {"MinimumLevel": "Information", "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss} {Level:u3}] [{Environment}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "/app/logs/bulkimporter-pilot-.log", "rollingInterval": "Day", "retainedFileCountLimit": 14, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] [{Environment}] {CorrelationId} {Message:lj} {Properties:j}{NewLine}{Exception}"}}], "Properties": {"Application": "FleetXQ.BulkImporter", "Environment": "Pilot"}}, "Environment": {"Name": "Pilot", "Description": "Pilot testing environment", "RequiresApproval": true, "MaxOperationSize": 10000, "NotificationWebhooks": ["https://pilot-notifications.fleetxq.com/webhooks/bulkimporter"]}}