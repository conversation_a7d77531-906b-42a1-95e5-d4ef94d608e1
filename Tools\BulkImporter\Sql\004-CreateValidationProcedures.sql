-- =============================================
-- FleetXQ Bulk Importer - Validation Procedures
-- Creates stored procedures for validating staging data
-- =============================================

-- =============================================
-- Procedure: usp_ValidateDriverImport
-- Validates driver staging data and resolves FK relationships
-- =============================================
IF OBJECT_ID('[Staging].[usp_ValidateDriverImport]', 'P') IS NOT NULL
    DROP PROCEDURE [Staging].[usp_ValidateDriverImport]
GO

CREATE PROCEDURE [Staging].[usp_ValidateDriverImport]
    @ImportSessionId UNIQUEIDENTIFIER
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @ErrorMessage NVARCHAR(MAX);
    DECLARE @RowCount INT = 0;
    
    BEGIN TRY
        -- Reset validation status for this session
        UPDATE [Staging].[DriverImport]
        SET [ValidationStatus] = 'Pending',
            [ValidationErrors] = NULL,
            [CustomerId] = NULL,
            [SiteId] = NULL,
            [DepartmentId] = NULL,
            [ExistingPersonId] = NULL,
            [ExistingDriverId] = NULL,
            [ProcessingAction] = NULL
        WHERE [ImportSessionId] = @ImportSessionId;
        
        -- Step 1: Validate required fields
        UPDATE [Staging].[DriverImport]
        SET [ValidationStatus] = 'Invalid',
            [ValidationErrors] = 'Missing required fields: ' + 
                CASE WHEN [PersonFirstName] IS NULL OR [PersonFirstName] = '' THEN 'FirstName ' ELSE '' END +
                CASE WHEN [PersonLastName] IS NULL OR [PersonLastName] = '' THEN 'LastName ' ELSE '' END +
                CASE WHEN [CustomerName] IS NULL OR [CustomerName] = '' THEN 'CustomerName ' ELSE '' END +
                CASE WHEN [SiteName] IS NULL OR [SiteName] = '' THEN 'SiteName ' ELSE '' END +
                CASE WHEN [DepartmentName] IS NULL OR [DepartmentName] = '' THEN 'DepartmentName ' ELSE '' END
        WHERE [ImportSessionId] = @ImportSessionId
            AND [ValidationStatus] = 'Pending'
            AND ([PersonFirstName] IS NULL OR [PersonFirstName] = '' OR
                 [PersonLastName] IS NULL OR [PersonLastName] = '' OR
                 [CustomerName] IS NULL OR [CustomerName] = '' OR
                 [SiteName] IS NULL OR [SiteName] = '' OR
                 [DepartmentName] IS NULL OR [DepartmentName] = '');
        
        SET @RowCount = @@ROWCOUNT;
        IF @RowCount > 0
            PRINT CONCAT('Marked ', @RowCount, ' rows as invalid due to missing required fields');
        
        -- Step 2: Resolve Customer references with dealer validation
        UPDATE di
        SET [CustomerId] = c.[Id],
            [CustomerDealerId] = c.[DealerId]
        FROM [Staging].[DriverImport] di
        INNER JOIN [dbo].[Customer] c ON LTRIM(RTRIM(di.[CustomerName])) = LTRIM(RTRIM(c.[CompanyName]))
        WHERE di.[ImportSessionId] = @ImportSessionId
            AND di.[ValidationStatus] = 'Pending';
        
        -- Validate customer belongs to selected dealer (if dealer scoping is enabled)
        UPDATE di
        SET [DealerValidationError] = CONCAT('Customer "', di.[CustomerName], '" does not belong to selected dealer'),
            [ValidationStatus] = 'Invalid'
        FROM [Staging].[DriverImport] di
        INNER JOIN [Staging].[ImportSession] iss ON di.[ImportSessionId] = iss.[Id]
        WHERE di.[ImportSessionId] = @ImportSessionId
            AND di.[ValidationStatus] = 'Pending'
            AND iss.[DealerValidationEnabled] = 1
            AND iss.[SelectedDealerId] IS NOT NULL
            AND (di.[CustomerDealerId] != iss.[SelectedDealerId] OR di.[CustomerDealerId] IS NULL);
        
        -- Mark rows with invalid Customer references
        UPDATE [Staging].[DriverImport]
        SET [ValidationStatus] = 'Invalid',
            [ValidationErrors] = CONCAT('Customer not found: ', [CustomerName])
        WHERE [ImportSessionId] = @ImportSessionId
            AND [ValidationStatus] = 'Pending'
            AND [CustomerId] IS NULL;
            
        SET @RowCount = @@ROWCOUNT;
        IF @RowCount > 0
            PRINT CONCAT('Marked ', @RowCount, ' rows as invalid due to Customer lookup failures');
        
        -- Step 3: Resolve Site references
        UPDATE di
        SET [SiteId] = s.[Id]
        FROM [Staging].[DriverImport] di
        INNER JOIN [dbo].[Site] s ON LTRIM(RTRIM(di.[SiteName])) = LTRIM(RTRIM(s.[SiteName]))
            AND di.[CustomerId] = s.[CustomerId]
        WHERE di.[ImportSessionId] = @ImportSessionId
            AND di.[ValidationStatus] = 'Pending';
        
        -- Mark rows with invalid Site references
        UPDATE [Staging].[DriverImport]
        SET [ValidationStatus] = 'Invalid',
            [ValidationErrors] = CONCAT('Site not found: ', [SiteName], ' for Customer: ', [CustomerName])
        WHERE [ImportSessionId] = @ImportSessionId
            AND [ValidationStatus] = 'Pending'
            AND [SiteId] IS NULL;
            
        SET @RowCount = @@ROWCOUNT;
        IF @RowCount > 0
            PRINT CONCAT('Marked ', @RowCount, ' rows as invalid due to Site lookup failures');
        
        -- Step 4: Resolve Department references
        UPDATE di
        SET [DepartmentId] = d.[Id]
        FROM [Staging].[DriverImport] di
        INNER JOIN [dbo].[Department] d ON LTRIM(RTRIM(di.[DepartmentName])) = LTRIM(RTRIM(d.[DepartmentName]))
            AND di.[SiteId] = d.[SiteId]
        WHERE di.[ImportSessionId] = @ImportSessionId
            AND di.[ValidationStatus] = 'Pending';
        
        -- Mark rows with invalid Department references
        UPDATE [Staging].[DriverImport]
        SET [ValidationStatus] = 'Invalid',
            [ValidationErrors] = CONCAT('Department not found: ', [DepartmentName], ' for Site: ', [SiteName])
        WHERE [ImportSessionId] = @ImportSessionId
            AND [ValidationStatus] = 'Pending'
            AND [DepartmentId] IS NULL;
            
        SET @RowCount = @@ROWCOUNT;
        IF @RowCount > 0
            PRINT CONCAT('Marked ', @RowCount, ' rows as invalid due to Department lookup failures');
        
        -- Step 5: Check for existing Person records (by email if provided)
        UPDATE di
        SET [ExistingPersonId] = p.[Id]
        FROM [Staging].[DriverImport] di
        INNER JOIN [dbo].[Person] p ON LTRIM(RTRIM(di.[PersonEmail])) = LTRIM(RTRIM(p.[Email]))
            AND di.[CustomerId] = p.[CustomerId]
        WHERE di.[ImportSessionId] = @ImportSessionId
            AND di.[ValidationStatus] = 'Pending'
            AND di.[PersonEmail] IS NOT NULL
            AND di.[PersonEmail] != '';
        
        -- Step 6: Check for existing Person records (by name + customer if no email match)
        UPDATE di
        SET [ExistingPersonId] = p.[Id]
        FROM [Staging].[DriverImport] di
        INNER JOIN [dbo].[Person] p ON LTRIM(RTRIM(di.[PersonFirstName])) = LTRIM(RTRIM(p.[FirstName]))
            AND LTRIM(RTRIM(di.[PersonLastName])) = LTRIM(RTRIM(p.[LastName]))
            AND di.[CustomerId] = p.[CustomerId]
        WHERE di.[ImportSessionId] = @ImportSessionId
            AND di.[ValidationStatus] = 'Pending'
            AND di.[ExistingPersonId] IS NULL;
        
        -- Step 7: Check for existing Driver records linked to found Persons
        UPDATE di
        SET [ExistingDriverId] = p.[DriverId]
        FROM [Staging].[DriverImport] di
        INNER JOIN [dbo].[Person] p ON di.[ExistingPersonId] = p.[Id]
        WHERE di.[ImportSessionId] = @ImportSessionId
            AND di.[ValidationStatus] = 'Pending'
            AND di.[ExistingPersonId] IS NOT NULL
            AND p.[DriverId] IS NOT NULL;
        
        -- Step 8: Determine processing action
        UPDATE [Staging].[DriverImport]
        SET [ProcessingAction] = CASE 
            WHEN [ExistingPersonId] IS NOT NULL AND [ExistingDriverId] IS NOT NULL THEN 'Update'
            WHEN [ExistingPersonId] IS NOT NULL AND [ExistingDriverId] IS NULL THEN 'Insert'
            ELSE 'Insert'
        END,
        [ValidationStatus] = 'Valid'
        WHERE [ImportSessionId] = @ImportSessionId
            AND [ValidationStatus] = 'Pending';
        
        -- Final summary
        SELECT 
            [ValidationStatus],
            [ProcessingAction],
            COUNT(*) as [RowCount]
        FROM [Staging].[DriverImport]
        WHERE [ImportSessionId] = @ImportSessionId
        GROUP BY [ValidationStatus], [ProcessingAction]
        ORDER BY [ValidationStatus], [ProcessingAction];
        
        PRINT 'Driver import validation completed successfully';
        
    END TRY
    BEGIN CATCH
        SET @ErrorMessage = CONCAT('Driver validation failed: ', ERROR_MESSAGE());
        PRINT @ErrorMessage;
        THROW;
    END CATCH
END
GO

-- =============================================
-- Procedure: usp_ValidateVehicleImport
-- Validates vehicle staging data and resolves FK relationships
-- =============================================
IF OBJECT_ID('[Staging].[usp_ValidateVehicleImport]', 'P') IS NOT NULL
    DROP PROCEDURE [Staging].[usp_ValidateVehicleImport]
GO

CREATE PROCEDURE [Staging].[usp_ValidateVehicleImport]
    @ImportSessionId UNIQUEIDENTIFIER
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @ErrorMessage NVARCHAR(MAX);
    DECLARE @RowCount INT = 0;
    
    BEGIN TRY
        -- Reset validation status for this session
        UPDATE [Staging].[VehicleImport]
        SET [ValidationStatus] = 'Pending',
            [ValidationErrors] = NULL,
            [CustomerId] = NULL,
            [SiteId] = NULL,
            [DepartmentId] = NULL,
            [ModelId] = NULL,
            [ModuleId] = NULL,
            [AssignedDriverId] = NULL,
            [AssignedPersonId] = NULL,
            [ExistingVehicleId] = NULL,
            [ProcessingAction] = NULL
        WHERE [ImportSessionId] = @ImportSessionId;
        
        -- Step 1: Validate required fields
        UPDATE [Staging].[VehicleImport]
        SET [ValidationStatus] = 'Invalid',
            [ValidationErrors] = 'Missing required fields: ' + 
                CASE WHEN [HireNo] IS NULL OR [HireNo] = '' THEN 'HireNo ' ELSE '' END +
                CASE WHEN [SerialNo] IS NULL OR [SerialNo] = '' THEN 'SerialNo ' ELSE '' END +
                CASE WHEN [CustomerName] IS NULL OR [CustomerName] = '' THEN 'CustomerName ' ELSE '' END +
                CASE WHEN [SiteName] IS NULL OR [SiteName] = '' THEN 'SiteName ' ELSE '' END +
                CASE WHEN [DepartmentName] IS NULL OR [DepartmentName] = '' THEN 'DepartmentName ' ELSE '' END +
                CASE WHEN [ModelName] IS NULL OR [ModelName] = '' THEN 'ModelName ' ELSE '' END +
                CASE WHEN [ModuleSerialNumber] IS NULL OR [ModuleSerialNumber] = '' THEN 'ModuleSerialNumber ' ELSE '' END
        WHERE [ImportSessionId] = @ImportSessionId
            AND [ValidationStatus] = 'Pending'
            AND ([HireNo] IS NULL OR [HireNo] = '' OR
                 [SerialNo] IS NULL OR [SerialNo] = '' OR
                 [CustomerName] IS NULL OR [CustomerName] = '' OR
                 [SiteName] IS NULL OR [SiteName] = '' OR
                 [DepartmentName] IS NULL OR [DepartmentName] = '' OR
                 [ModelName] IS NULL OR [ModelName] = '' OR
                 [ModuleSerialNumber] IS NULL OR [ModuleSerialNumber] = '');
        
        SET @RowCount = @@ROWCOUNT;
        IF @RowCount > 0
            PRINT CONCAT('Marked ', @RowCount, ' rows as invalid due to missing required fields');
        
        -- Step 2: Resolve Customer references with dealer validation
        UPDATE vi
        SET [CustomerId] = c.[Id],
            [CustomerDealerId] = c.[DealerId]
        FROM [Staging].[VehicleImport] vi
        INNER JOIN [dbo].[Customer] c ON LTRIM(RTRIM(vi.[CustomerName])) = LTRIM(RTRIM(c.[CompanyName]))
        WHERE vi.[ImportSessionId] = @ImportSessionId
            AND vi.[ValidationStatus] = 'Pending';
        
        -- Validate customer belongs to selected dealer (if dealer scoping is enabled)
        UPDATE vi
        SET [DealerValidationError] = CONCAT('Customer "', vi.[CustomerName], '" does not belong to selected dealer'),
            [ValidationStatus] = 'Invalid'
        FROM [Staging].[VehicleImport] vi
        INNER JOIN [Staging].[ImportSession] iss ON vi.[ImportSessionId] = iss.[Id]
        WHERE vi.[ImportSessionId] = @ImportSessionId
            AND vi.[ValidationStatus] = 'Pending'
            AND iss.[DealerValidationEnabled] = 1
            AND iss.[SelectedDealerId] IS NOT NULL
            AND (vi.[CustomerDealerId] != iss.[SelectedDealerId] OR vi.[CustomerDealerId] IS NULL);
        
        -- Step 3: Resolve Site references
        UPDATE vi
        SET [SiteId] = s.[Id]
        FROM [Staging].[VehicleImport] vi
        INNER JOIN [dbo].[Site] s ON LTRIM(RTRIM(vi.[SiteName])) = LTRIM(RTRIM(s.[SiteName]))
            AND vi.[CustomerId] = s.[CustomerId]
        WHERE vi.[ImportSessionId] = @ImportSessionId
            AND vi.[ValidationStatus] = 'Pending';
        
        -- Step 4: Resolve Department references
        UPDATE vi
        SET [DepartmentId] = d.[Id]
        FROM [Staging].[VehicleImport] vi
        INNER JOIN [dbo].[Department] d ON LTRIM(RTRIM(vi.[DepartmentName])) = LTRIM(RTRIM(d.[DepartmentName]))
            AND vi.[SiteId] = d.[SiteId]
        WHERE vi.[ImportSessionId] = @ImportSessionId
            AND vi.[ValidationStatus] = 'Pending';
        
        -- Step 5: Resolve Model references
        UPDATE vi
        SET [ModelId] = m.[Id]
        FROM [Staging].[VehicleImport] vi
        INNER JOIN [dbo].[Model] m ON LTRIM(RTRIM(vi.[ModelName])) = LTRIM(RTRIM(m.[ModelName]))
        WHERE vi.[ImportSessionId] = @ImportSessionId
            AND vi.[ValidationStatus] = 'Pending';
        
        -- Step 6: Resolve Module references with dealer validation
        UPDATE vi
        SET [ModuleId] = m.[Id],
            [ModuleDealerId] = m.[DealerId],
            [ModuleAvailabilityStatus] = CASE 
                WHEN m.[ModuleStatus] = 'Available' AND (m.[IsAllocatedToVehicle] = 0 OR m.[IsAllocatedToVehicle] IS NULL) THEN 'Available'
                WHEN m.[ModuleStatus] = 'Assigned' AND m.[IsAllocatedToVehicle] = 1 THEN 'Assigned'
                ELSE m.[ModuleStatus]
            END
        FROM [Staging].[VehicleImport] vi
        INNER JOIN [dbo].[Module] m ON LTRIM(RTRIM(vi.[ModuleSerialNumber])) = LTRIM(RTRIM(m.[TechNumber]))
        WHERE vi.[ImportSessionId] = @ImportSessionId
            AND vi.[ValidationStatus] = 'Pending';
        
        -- Validate module belongs to selected dealer
        UPDATE vi
        SET [DealerValidationError] = CONCAT([DealerValidationError], 
                CASE WHEN [DealerValidationError] IS NOT NULL THEN '; ' ELSE '' END,
                'Module "', vi.[ModuleSerialNumber], '" does not belong to selected dealer'),
            [ValidationStatus] = 'Invalid'
        FROM [Staging].[VehicleImport] vi
        INNER JOIN [Staging].[ImportSession] iss ON vi.[ImportSessionId] = iss.[Id]
        WHERE vi.[ImportSessionId] = @ImportSessionId
            AND vi.[ValidationStatus] = 'Pending'
            AND iss.[DealerValidationEnabled] = 1
            AND iss.[SelectedDealerId] IS NOT NULL
            AND (vi.[ModuleDealerId] != iss.[SelectedDealerId] OR vi.[ModuleDealerId] IS NULL);
        
        -- Validate module availability
        UPDATE vi
        SET [DealerValidationError] = CONCAT([DealerValidationError], 
                CASE WHEN [DealerValidationError] IS NOT NULL THEN '; ' ELSE '' END,
                'Module "', vi.[ModuleSerialNumber], '" is not available (Status: ', vi.[ModuleAvailabilityStatus], ')'),
            [ValidationStatus] = 'Invalid'
        FROM [Staging].[VehicleImport] vi
        WHERE vi.[ImportSessionId] = @ImportSessionId
            AND vi.[ValidationStatus] = 'Pending'
            AND vi.[ModuleAvailabilityStatus] != 'Available'
            AND vi.[ModuleId] IS NOT NULL;
        
        -- Step 3: Check for existing Vehicle records (by HireNo)
        UPDATE vi
        SET [ExistingVehicleId] = v.[Id]
        FROM [Staging].[VehicleImport] vi
        INNER JOIN [dbo].[Vehicle] v ON LTRIM(RTRIM(vi.[HireNo])) = LTRIM(RTRIM(v.[HireNo]))
        WHERE vi.[ImportSessionId] = @ImportSessionId
            AND vi.[ValidationStatus] = 'Pending';
        
        -- Mark as valid if all lookups succeeded
        UPDATE [Staging].[VehicleImport]
        SET [ValidationStatus] = 'Valid',
            [ProcessingAction] = CASE 
                WHEN [ExistingVehicleId] IS NOT NULL THEN 'Update'
                ELSE 'Insert'
            END
        WHERE [ImportSessionId] = @ImportSessionId
            AND [ValidationStatus] = 'Pending'
            AND [CustomerId] IS NOT NULL
            AND [SiteId] IS NOT NULL
            AND [DepartmentId] IS NOT NULL
            AND [ModelId] IS NOT NULL
            AND [ModuleId] IS NOT NULL;
        
        PRINT 'Vehicle import validation completed successfully';
        
    END TRY
    BEGIN CATCH
        SET @ErrorMessage = CONCAT('Vehicle validation failed: ', ERROR_MESSAGE());
        PRINT @ErrorMessage;
        THROW;
    END CATCH
END
GO

PRINT 'Validation procedures created successfully'