# FleetXQ BulkImporter - Multi-Environment Deployment Guide

## Overview

The FleetXQ BulkImporter has been enhanced to support deployment across multiple environments (Development, Staging, Pilot, Production) with environment-specific configuration management, automated deployment scripts, and comprehensive security features.

## Supported Environments

### 1. **Development**
- **Purpose**: Local development and testing
- **Database**: LocalDB or local SQL Server instance
- **Configuration**: Minimal security, debug logging, small data sets
- **Deployment**: Local execution via `dotnet run`

### 2. **Staging** 
- **Purpose**: Integration testing and deployment validation
- **Database**: Dedicated staging database server
- **Configuration**: Production-like settings, verbose logging
- **Deployment**: Containerized or service-based deployment

### 3. **Pilot**
- **Purpose**: Limited production testing with real data
- **Database**: Isolated pilot database
- **Configuration**: Production settings with enhanced monitoring
- **Deployment**: Containerized with approval workflows

### 4. **Production**
- **Purpose**: Live production environment
- **Database**: Production database cluster
- **Configuration**: Optimized performance, security hardened
- **Deployment**: Full CI/CD with approval gates

## Environment Configuration

### Configuration Hierarchy

The application uses the standard .NET configuration hierarchy:

1. **Base Configuration**: `appsettings.json`
2. **Environment Override**: `appsettings.{Environment}.json`
3. **Environment Variables**: `FLEETXQ_BULKIMPORTER_*`
4. **Command Line Arguments**: `--key value`

### Environment Variables

All configuration can be overridden using environment variables with the prefix `FLEETXQ_BULKIMPORTER_`:

```bash
# Database connection
FLEETXQ_BULKIMPORTER_ConnectionStrings__FleetXQConnection="Server=...;Database=..."

# Environment settings
FLEETXQ_BULKIMPORTER_Environment__Name="Production"
FLEETXQ_BULKIMPORTER_Environment__MaxOperationSize="100000"

# Bulk importer settings
FLEETXQ_BULKIMPORTER_BulkImporter__DefaultBatchSize="2000"
FLEETXQ_BULKIMPORTER_BulkImporter__ValidationEnabled="true"

# Notification webhooks
FLEETXQ_BULKIMPORTER_Environment__NotificationWebhooks__0="https://alerts.company.com/webhook"
```

### Secret Management

#### Development
- Secrets stored in `appsettings.Development.json` (not committed to source control)
- Use SQL Server integrated authentication when possible

#### Staging/Pilot/Production
- Database passwords via environment variables or Azure Key Vault
- API keys and webhook URLs via secure configuration providers
- Never store secrets in configuration files

## Deployment Methods

### Method 1: Local Deployment (Development)

```bash
# Set environment
export ASPNETCORE_ENVIRONMENT=Development

# Run application
cd Tools/BulkImporter
dotnet run

# Or with specific environment override
dotnet run --environment Development
```

### Method 2: Service Deployment (Windows)

```powershell
# Build and deploy
.\Deploy\deploy.ps1 -Environment Staging -Version "1.2.3" -ConfigureSecrets

# Install as Windows service
sc create "FleetXQ.BulkImporter.Staging" binPath="C:\Apps\FleetXQ\BulkImporter\FleetXQ.Tools.BulkImporter.exe"
sc config "FleetXQ.BulkImporter.Staging" start=manual
```

### Method 3: Container Deployment (Linux)

```bash
# Build container
docker build -f Deploy/docker/Dockerfile -t fleetxq/bulkimporter:1.2.3 .

# Run with environment-specific configuration
docker-compose --profile staging up -d

# Or run directly
docker run -d \
  --name fleetxq-bulkimporter-staging \
  -e ASPNETCORE_ENVIRONMENT=Staging \
  -e FLEETXQ_BULKIMPORTER_ConnectionStrings__FleetXQConnection="Server=..." \
  -v /app/data:/app/data \
  -v /app/logs:/app/logs \
  fleetxq/bulkimporter:1.2.3
```

### Method 4: Linux Service Deployment

```bash
# Setup environment
sudo ./Deploy/environment-setup.sh staging --setup-database --configure-secrets --install-service

# Deploy application
sudo systemctl start fleetxq-bulkimporter-staging
sudo systemctl enable fleetxq-bulkimporter-staging
```

## Environment-Specific Configurations

### Development Environment

```json
{
  "ConnectionStrings": {
    "FleetXQConnection": "Server=(localdb)\\mssqllocaldb;Database=FleetXQ_Dev;Trusted_Connection=true;"
  },
  "BulkImporter": {
    "DefaultDriversCount": 100,
    "DefaultVehiclesCount": 50,
    "DefaultBatchSize": 100,
    "MaxBatchSize": 1000,
    "ValidationEnabled": true,
    "StopOnFirstError": true,
    "CleanupStagingData": false
  },
  "Environment": {
    "Name": "Development",
    "RequiresApproval": false,
    "MaxOperationSize": 10000
  }
}
```

### Production Environment

```json
{
  "ConnectionStrings": {
    "FleetXQConnection": "Server=prod-cluster;Database=FleetXQ;User Id=app_user;Password=***********;"
  },
  "BulkImporter": {
    "DefaultDriversCount": 50000,
    "DefaultVehiclesCount": 25000,
    "DefaultBatchSize": 2000,
    "MaxBatchSize": 50000,
    "ValidationEnabled": true,
    "StopOnFirstError": false,
    "CleanupStagingData": true
  },
  "Environment": {
    "Name": "Production",
    "RequiresApproval": true,
    "MaxOperationSize": 100000,
    "NotificationWebhooks": [
      "https://prod-alerts.fleetxq.com/webhooks/bulkimporter"
    ],
    "MaintenanceWindows": [
      {
        "Start": "02:00",
        "End": "04:00",
        "TimeZone": "UTC",
        "Description": "Daily maintenance window"
      }
    ]
  }
}
```

## Security Considerations

### Network Security
- **Development**: No special requirements
- **Staging/Pilot**: VPN or private network access
- **Production**: Locked down network, firewall rules, VPN required

### Database Security
- **Development**: Integrated authentication
- **Staging/Pilot**: Dedicated service accounts with limited permissions
- **Production**: Encrypted connections, certificate validation, audit logging

### File System Security
- **All Environments**: Restricted file permissions, separate log/data directories
- **Production**: Encrypted storage, regular backup, monitoring

### Secret Management
- **Development**: Local configuration files (excluded from source control)
- **Staging/Pilot**: Environment variables or Azure Key Vault
- **Production**: Azure Key Vault, HSM, or enterprise secret management

## Operation Procedures

### Pre-Deployment Checklist

#### All Environments
- [ ] Application builds successfully
- [ ] All tests pass
- [ ] Configuration validated
- [ ] Database objects deployed

#### Staging/Pilot/Production
- [ ] Environment-specific secrets configured
- [ ] Database connection tested
- [ ] Monitoring and alerting configured
- [ ] Backup procedures verified
- [ ] Rollback plan prepared

### Production Deployment Checklist

- [ ] **Change Management**: Change request approved
- [ ] **Database Backup**: Full backup completed
- [ ] **Maintenance Window**: Deployment scheduled during approved window
- [ ] **Version Testing**: Version tested in Staging and Pilot
- [ ] **Monitoring**: Enhanced monitoring enabled
- [ ] **Rollback Plan**: Rollback procedure documented and tested
- [ ] **Stakeholder Notification**: All stakeholders informed
- [ ] **Post-Deployment Verification**: Success criteria defined

### Monitoring and Alerting

#### Metrics to Monitor
- **Application Health**: Process status, memory usage, CPU usage
- **Operation Metrics**: Records processed, success/failure rates, duration
- **Database Health**: Connection pool, query performance, deadlocks
- **File System**: Disk space, log file sizes, archive cleanup

#### Alert Thresholds
- **Warning**: Operation duration > 1 hour, error rate > 5%
- **Critical**: Process crash, database connectivity lost, disk space < 10%
- **Info**: Large operations (> 10,000 records), maintenance window operations

## Troubleshooting

### Common Issues

#### Configuration Problems
```bash
# Check environment detection
dotnet FleetXQ.Tools.BulkImporter.dll --help

# Validate configuration
grep -i error logs/bulkimporter-*.log

# Test database connectivity
sqlcmd -S server -d database -U user -P password -Q "SELECT 1"
```

#### Performance Issues
```bash
# Check memory usage
top -p $(pgrep -f BulkImporter)

# Monitor database connections
SELECT * FROM sys.dm_exec_sessions WHERE program_name LIKE '%BulkImporter%'

# Review batch sizes and timeouts in configuration
```

#### File System Issues
```bash
# Check disk space
df -h /app/data

# Review file permissions
ls -la /app/data/

# Clean up old logs/archives
find /app/data/archive -mtime +30 -delete
```

### Log Analysis

#### Log Locations
- **Development**: `logs/bulkimporter-dev-*.log`
- **Staging**: `/app/logs/bulkimporter-staging-*.log`
- **Pilot**: `/app/logs/bulkimporter-pilot-*.log`
- **Production**: `/app/logs/bulkimporter-prod-*.log`

#### Log Patterns
```bash
# Find errors
grep "ERROR\|FATAL" logs/bulkimporter-*.log

# Check operation performance
grep "Import completed" logs/bulkimporter-*.log | tail -10

# Monitor memory usage
grep "Memory usage" logs/bulkimporter-*.log

# Review environment-specific warnings
grep "Environment\|Maintenance" logs/bulkimporter-*.log
```

## Maintenance

### Regular Tasks

#### Daily
- Check application logs for errors
- Verify disk space in data directories
- Monitor operation success rates

#### Weekly  
- Review performance metrics
- Clean up old archive files
- Validate backup procedures

#### Monthly
- Update security certificates
- Review and update secrets
- Performance tuning and optimization
- Documentation updates

### Disaster Recovery

#### Backup Strategy
- **Database**: Full backup before large operations
- **Configuration**: Version-controlled configuration files
- **Logs**: Centralized log aggregation and retention
- **Data**: Regular backup of output and archive directories

#### Recovery Procedures
1. **Service Failure**: Restart service, check logs, escalate if needed
2. **Database Issues**: Restore from backup, validate data integrity
3. **Configuration Corruption**: Restore from version control
4. **Data Loss**: Restore from backup, re-run failed operations

## Support and Escalation

### Contact Information
- **Development Issues**: Development Team
- **Environment Issues**: DevOps Team  
- **Production Issues**: On-call Engineer
- **Security Issues**: Security Team

### Escalation Matrix
- **Level 1**: Standard operational issues
- **Level 2**: Service degradation, configuration problems
- **Level 3**: Service outage, data corruption, security incidents
- **Level 4**: Business-critical failure, data breach

---

For additional help, see:
- [SETUP.md](SETUP.md) - Initial setup and configuration
- [README.md](README.md) - Usage and command-line options
- Application logs - Detailed error and operation information
