-- =============================================
-- FleetXQ Bulk Importer - Enhanced Module Tracking
-- Adds module status tracking and allocation history
-- =============================================

-- Add Module Status field if it doesn't exist
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('[dbo].[Module]') AND name = 'ModuleStatus')
BEGIN
    ALTER TABLE [dbo].[Module] 
    ADD [ModuleStatus] NVARCHAR(20) DEFAULT 'Available';
    
    PRINT 'Added ModuleStatus column to Module table';
END
ELSE
BEGIN
    PRINT 'ModuleStatus column already exists in Module table';
END
GO

-- Update existing modules to have default status
UPDATE [dbo].[Module] 
SET [ModuleStatus] = CASE 
    WHEN [IsAllocatedToVehicle] = 1 THEN 'Assigned'
    WHEN [IsAllocatedToVehicle] = 0 THEN 'Available'
    ELSE 'Available'
END
WHERE [ModuleStatus] IS NULL;
GO

-- Create Module Allocation History table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE schema_id = SCHEMA_ID('dbo') AND name = 'ModuleAllocationHistory')
BEGIN
    CREATE TABLE [dbo].[ModuleAllocationHistory] (
        [Id] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        [ModuleId] UNIQUEIDENTIFIER NOT NULL,
        [VehicleId] UNIQUEIDENTIFIER NULL,
        [AllocationDate] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        [DeallocationDate] DATETIME2 NULL,
        [AllocationType] NVARCHAR(20) NOT NULL, -- Assignment, Swap, Maintenance, Return, Spare
        [RequestedBy] NVARCHAR(100) NULL,
        [Notes] NVARCHAR(500) NULL,
        [DealerId] UNIQUEIDENTIFIER NOT NULL,
        [ImportSessionId] UNIQUEIDENTIFIER NULL, -- Track bulk import operations
        
        -- Indexes for performance
        INDEX IX_ModuleAllocationHistory_Module ([ModuleId]),
        INDEX IX_ModuleAllocationHistory_Vehicle ([VehicleId]),
        INDEX IX_ModuleAllocationHistory_Dealer ([DealerId]),
        INDEX IX_ModuleAllocationHistory_Date ([AllocationDate] DESC),
        INDEX IX_ModuleAllocationHistory_Session ([ImportSessionId]) WHERE [ImportSessionId] IS NOT NULL
    );
    
    PRINT 'Created ModuleAllocationHistory table';
END
ELSE
BEGIN
    PRINT 'ModuleAllocationHistory table already exists';
END
GO

-- Add foreign key constraints
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_ModuleAllocationHistory_Module')
BEGIN
    ALTER TABLE [dbo].[ModuleAllocationHistory]
    ADD CONSTRAINT FK_ModuleAllocationHistory_Module 
        FOREIGN KEY ([ModuleId]) REFERENCES [dbo].[Module]([Id]);
    
    PRINT 'Added FK constraint: ModuleAllocationHistory -> Module';
END
GO

IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_ModuleAllocationHistory_Vehicle')
BEGIN
    ALTER TABLE [dbo].[ModuleAllocationHistory]
    ADD CONSTRAINT FK_ModuleAllocationHistory_Vehicle 
        FOREIGN KEY ([VehicleId]) REFERENCES [dbo].[Vehicle]([Id]);
    
    PRINT 'Added FK constraint: ModuleAllocationHistory -> Vehicle';
END
GO

IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_ModuleAllocationHistory_Dealer')
BEGIN
    ALTER TABLE [dbo].[ModuleAllocationHistory]
    ADD CONSTRAINT FK_ModuleAllocationHistory_Dealer 
        FOREIGN KEY ([DealerId]) REFERENCES [dbo].[Dealer]([Id]);
    
    PRINT 'Added FK constraint: ModuleAllocationHistory -> Dealer';
END
GO

IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_ModuleAllocationHistory_ImportSession')
BEGIN
    ALTER TABLE [dbo].[ModuleAllocationHistory]
    ADD CONSTRAINT FK_ModuleAllocationHistory_ImportSession 
        FOREIGN KEY ([ImportSessionId]) REFERENCES [Staging].[ImportSession]([Id]);
    
    PRINT 'Added FK constraint: ModuleAllocationHistory -> ImportSession';
END
GO

-- Create initial allocation history for existing vehicle-module assignments
INSERT INTO [dbo].[ModuleAllocationHistory] 
    ([ModuleId], [VehicleId], [AllocationDate], [AllocationType], [RequestedBy], [Notes], [DealerId])
SELECT DISTINCT
    v.[ModuleId1],
    v.[Id],
    COALESCE(v.[HireTime], GETUTCDATE()),
    'Assignment',
    'System Migration',
    'Historical assignment created during module tracking enhancement',
    COALESCE(m.[DealerId], c.[DealerId])
FROM [dbo].[Vehicle] v
INNER JOIN [dbo].[Module] m ON v.[ModuleId1] = m.[Id]
INNER JOIN [dbo].[Customer] c ON v.[CustomerId] = c.[Id]
WHERE NOT EXISTS (
    SELECT 1 FROM [dbo].[ModuleAllocationHistory] mah 
    WHERE mah.[ModuleId] = v.[ModuleId1] AND mah.[VehicleId] = v.[Id]
)
AND v.[ModuleId1] IS NOT NULL;

PRINT CONCAT('Created historical allocation records for ', @@ROWCOUNT, ' existing vehicle-module assignments');
GO

PRINT 'Enhanced module tracking setup completed successfully';
