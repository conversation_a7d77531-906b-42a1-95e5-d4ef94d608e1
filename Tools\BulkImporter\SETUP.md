# FleetXQ BulkImporter - First-Time Setup

## Quick Setup Guide

### 1. Prerequisites
- .NET 6.0 SDK or later
- SQL Server with FleetXQ database
- Database permissions to create schemas, tables, and procedures

### 2. Initial Setup

1. **Clone and Build**
   ```bash
   cd Tools/BulkImporter
   dotnet restore
   dotnet build
   ```

2. **Configure Database Connection**
   - Edit `appsettings.json`
   - Update the connection string:
     ```json
     "ConnectionStrings": {
       "FleetXQConnection": "Server=YOUR_SERVER;Database=FleetXQ;Trusted_Connection=true;"
     }
     ```

3. **Setup Database Objects**
   Run the SQL scripts in order:
   ```sql
   -- 1. Create staging schema
   -- Run: 001-CreateStagingSchema.sql
   
   -- 2. Create staging tables
   -- Run: 002-CreateDriverStagingTable.sql
   -- Run: 003-CreateVehicleStagingTable.sql
   
   -- 3. Create stored procedures
   -- Run: 004-CreateValidationProcedures.sql
   -- Run: 005-CreateMergeProcedures.sql
   -- Run: 006-CreateDataGenerationProcedures.sql
   
   -- 4. Enhanced dealer and module tracking (NEW)
   -- Run: 007-EnhanceModuleTracking.sql
   -- Run: 008-UpdateStagingTablesForDealerScoping.sql
   -- Run: 009-CreateModuleAvailabilityView.sql
   -- Run: 010-CreateDealerValidationProcedures.sql
   ```

### 3. First Run

Test the setup with a dry run:
```bash
dotnet run -- --generate --dry-run --drivers 10 --vehicles 5
```

### 4. Dealer Configuration (NEW)

Configure dealer settings in `appsettings.json`:
```json
"BulkImporter": {
  "DealerValidationEnabled": true,
  "RequireDealerSelection": true,
  "DefaultDealerId": null,
  "DefaultDealerName": null
}
```

### 5. Environment Configuration (NEW)

The BulkImporter now supports multiple environments. Set your target environment:

```bash
# For Development (default)
export ASPNETCORE_ENVIRONMENT=Development
dotnet run

# For Staging
export ASPNETCORE_ENVIRONMENT=Staging
dotnet run

# For Production
export ASPNETCORE_ENVIRONMENT=Production
dotnet run
```

### 6. Production Use

Once tested, run without `--dry-run`:
```bash
# Interactive mode (will prompt for dealer selection)
dotnet run

# Or non-interactive with dealer
dotnet run -- --drivers 1000 --vehicles 500 --dealer-id [GUID] --non-interactive

# Or with dealer name
dotnet run -- --drivers 1000 --vehicles 500 --dealer-name "Dealer Name" --non-interactive
```

### 7. Multi-Environment Deployment

For comprehensive deployment across environments, see [DEPLOYMENT.md](DEPLOYMENT.md).

## File Structure

Essential files for operation:
```
Tools/BulkImporter/
├── Configuration/
├── Logging/
├── Services/
├── Sql/                    # Database setup scripts
├── appsettings.json        # Main configuration
├── BulkImporter.csproj     # Project file
├── Program.cs              # Entry point
└── README.md               # User guide
```

## Troubleshooting

- **Connection Issues**: Verify connection string in `appsettings.json`
- **Permission Issues**: Ensure database user can create schemas and tables
- **Build Issues**: Run `dotnet restore` and check .NET SDK version
- **Runtime Issues**: Check logs in `logs/` directory
