{"ConnectionStrings": {"DefaultConnection": "Server=staging-db-server;Database=FleetXQ_Staging;User Id=bulkimporter_staging;Password=***********;MultipleActiveResultSets=true;Encrypt=true;TrustServerCertificate=false;", "FleetXQConnection": "Server=staging-db-server;Database=FleetXQ_Staging;User Id=bulkimporter_staging;Password=***********;MultipleActiveResultSets=true;Encrypt=true;TrustServerCertificate=false;"}, "BulkImporter": {"DefaultDriversCount": 10000, "DefaultVehiclesCount": 5000, "DefaultBatchSize": 1000, "MaxBatchSize": 25000, "BulkCopyTimeout": 900, "CommandTimeout": 240, "ValidationEnabled": true, "StopOnFirstError": false, "DealerValidationEnabled": true, "RequireDealerSelection": true, "CleanupStagingData": false}, "DataGeneration": {"OutputDirectory": "/app/data/output", "ArchiveDirectory": "/app/data/archive", "ErrorDirectory": "/app/data/errors", "EnableSyntheticDataGeneration": true, "RandomSeed": 54321, "GenerationBatchSize": 5000, "ValidateGeneratedData": true, "MaxMemoryUsageMB": 2000}, "Serilog": {"MinimumLevel": "Debug", "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss} {Level:u3}] [{Environment}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "/app/logs/bulkimporter-staging-.log", "rollingInterval": "Day", "retainedFileCountLimit": 10, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] [{Environment}] {CorrelationId} {Message:lj} {Properties:j}{NewLine}{Exception}"}}], "Properties": {"Application": "FleetXQ.BulkImporter", "Environment": "Staging"}}, "Environment": {"Name": "Staging", "Description": "Staging environment for testing deployment", "RequiresApproval": false, "MaxOperationSize": 50000, "NotificationWebhooks": ["https://staging-notifications.fleetxq.com/webhooks/bulkimporter"]}}